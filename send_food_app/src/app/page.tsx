// src/app/page.tsx
'use client';

import { useState } from 'react';
import ky from 'ky';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'; // Assuming shadcn/ui Card component is set up

export default function Home() {
  const [address, setAddress] = useState('');
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const handleSearch = async () => {
    if (!address) return;
    setLoading(true);
  
    try {
      // Step 1: Geocode the address
      const geocodeResponse = await ky.get(`/api/geocode?address=${encodeURIComponent(address)}`).json() as any;
  
      // IMPORTANT: Add this check to prevent the error
      if (!geocodeResponse.results || geocodeResponse.results.length === 0) {
        console.error('Geocoding failed: No results found for the address.');
        setResults([]); // Clear previous results
        return;
      }
  
      const { lat, lng } = geocodeResponse.results[0].geometry.location;
  
      // Step 2: Use coordinates to search for coffee shops
      const searchResponse = await ky.get(`/api/search?lat=${lat}&lng=${lng}`).json() as any;
      setResults(searchResponse);
  
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <main className="flex min-h-screen flex-col items-center p-24 bg-gray-50">
      <h1 className="text-4xl font-bold mb-8 text-center">Send Food</h1>
      <div className="flex w-full max-w-lg items-center space-x-2">
        <input
          type="text"
          placeholder="Enter your address..."
          value={address}
          onChange={(e) => setAddress(e.target.value)}
          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
        />
        <button
          onClick={handleSearch}
          disabled={loading}
          className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium h-10 px-4 py-2 bg-blue-600 text-white hover:bg-blue-700"
        >
          {loading ? 'Searching...' : 'Find Coffee'}
        </button>
      </div>

      <div className="mt-8 w-full max-w-lg space-y-4">
        {results.map((shop, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle>{shop.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Rating: {shop.rating}</p>
              <p>Source: {shop.source}</p>
              {/* You would add more details and the "Order on {Provider}" button here */}
            </CardContent>
          </Card>
        ))}
      </div>
    </main>
  );
}