// src/app/api/search/route.ts
import { NextResponse } from 'next/server';
import ky from 'ky';

// Utility function to fetch from Google Places API
async function searchGooglePlaces(lat: number, lng: number, apiKey: string) {
  const url = `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${lat},${lng}&radius=5000&type=cafe&key=${apiKey}`;
  const response = await ky(url).json() as any;
  return response.results.map((place: any) => ({
    source: 'Google Places',
    id: place.place_id,
    name: place.name,
    rating: place.rating,
    price_level: place.price_level,
    // Add other relevant fields
  }));
}

// Utility function to fetch from Yelp Fusion API
async function searchYelpFusion(lat: number, lng: number, apiKey: string) {
  const url = `https://api.yelp.com/v3/businesses/search?latitude=${lat}&longitude=${lng}&categories=coffee&transactions=delivery`;
  const response = await ky(url, {
    headers: {
      Authorization: `Bearer ${apiKey}`,
    },
  }).json() as any;
  return response.businesses.map((business: any) => ({
    source: 'Yelp Fusion',
    id: business.id,
    name: business.name,
    rating: business.rating,
    price_level: business.price,
    // Add other relevant fields
  }));
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const lat = searchParams.get('lat');
  const lng = searchParams.get('lng');

  if (!lat || !lng) {
    return NextResponse.json({ error: 'Latitude and longitude are required' }, { status: 400 });
  }

  const googleApiKey = process.env.GOOGLE_MAPS_API_KEY;
  const yelpApiKey = process.env.YELP_API_KEY;

  if (!googleApiKey || !yelpApiKey) {
    return NextResponse.json({ error: 'API keys are not set' }, { status: 500 });
  }

  try {
    // Fetch data from both APIs concurrently
    const [googleResults, yelpResults] = await Promise.all([
      searchGooglePlaces(parseFloat(lat), parseFloat(lng), googleApiKey),
      searchYelpFusion(parseFloat(lat), parseFloat(lng), yelpApiKey),
    ]);

    // Simple consolidation (you'd need more advanced logic for real-world de-duplication)
    const combinedResults = [...googleResults, ...yelpResults];

    return NextResponse.json(combinedResults);
  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json({ error: 'Failed to fetch search results' }, { status: 500 });
  }
}