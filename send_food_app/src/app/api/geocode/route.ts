// src/app/api/geocode/route.ts

import { NextResponse } from 'next/server';
import ky from 'ky';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const address = searchParams.get('address');

  if (!address) {
    return NextResponse.json({ error: 'Address is required' }, { status: 400 });
  }

  const apiKey = process.env.GOOGLE_MAPS_API_KEY;

  if (!apiKey) {
    return NextResponse.json({ error: 'Google Maps API key is not configured' }, { status: 500 });
  }

  const geocodeUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${apiKey}`;

  try {
    const response = await ky(geocodeUrl).json() as any;

    // Check for API errors
    if (response.status === 'REQUEST_DENIED') {
      console.error('Google Maps API Error:', response.error_message);
      return NextResponse.json({
        error: 'Google Maps API access denied. Please check your API key and ensure Geocoding API is enabled.',
        details: response.error_message
      }, { status: 403 });
    }

    if (response.status === 'ZERO_RESULTS') {
      return NextResponse.json({
        error: 'No results found for the provided address',
        results: []
      }, { status: 404 });
    }

    if (response.status !== 'OK') {
      console.error('Google Maps API Error:', response.status, response.error_message);
      return NextResponse.json({
        error: `Geocoding failed: ${response.status}`,
        details: response.error_message
      }, { status: 400 });
    }

    // In a real app, you would validate the response with Zod here
    return NextResponse.json(response);
  } catch (error) {
    console.error('Geocoding request failed:', error);
    return NextResponse.json({ error: 'Failed to geocode address' }, { status: 500 });
  }
}