sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant GooglePlaces
    participant YelpFusion
    participant ProviderSite

    User->>Frontend: Enters address
    Frontend->>Backend: /api/geocode(address)
    Backend->>GooglePlaces: Geocode request
    GooglePlaces-->>Backend: Geocoded lat/lng
    Backend-->>Frontend: Validated address & lat/lng

    Frontend->>Backend: /api/search(lat/lng)
    Backend->>GooglePlaces: Search nearby cafes/restaurants
    GooglePlaces-->>Backend: Google Places results (details, action links)
    Backend->>YelpFusion: Search nearby cafes (transactions=delivery)
    YelpFusion-->>Backend: Yelp Fusion results (details, delivery links)

    Backend->>Backend: Consolidate & enrich results
    Backend-->>Frontend: Display list of coffee shops

    User->>Frontend: Clicks "Order on {Provider}"
    Frontend->>ProviderSite: Deep-link (opens new tab)
    ProviderSite->>User: User completes order & payment